import platform #For CPU and operating system (type and version)
import socket #For Computer name, IP, and active ports
import time #For system time
def computerName():
    return platform.node() # return socket.gethostname()

def operatingSystem():
     return platform.system() + " " + platform.release() # platform.release() is better than platform.version()

def ipAddress():
    # Get the local hostname
    hostname = socket.gethostname()
    # Get the IP address associated with the hostname
    ip = socket.gethostbyname(hostname)
    return ip #Return IP only without extra text

def systemTime():
    # return time.strftime("%Y-%m-%d %H:%M:%S") #"%D" " " "%H:%M:%S" %D for date while %d for the day, %Y=2023 while %y=23
    return time.strftime("%d/%m/%Y %H:%M:%S") # This is the format needed by the assignment
print(computerName())
print(operatingSystem())
print(ipAddress())
print(systemTime())