#!/usr/bin/env python3
"""
Internet Speed Test Script
Uses the speedtest-cli module to test internet connection speed
"""

import speedtest
import sys
import time
from datetime import datetime

def format_speed(speed_bps):
    """Convert speed from bits per second to human readable format"""
    # Convert to Mbps
    speed_mbps = speed_bps / 1_000_000
    return f"{speed_mbps:.2f} Mbps"

def format_bytes(bytes_val):
    """Convert bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_val < 1024.0:
            return f"{bytes_val:.2f} {unit}"
        bytes_val /= 1024.0
    return f"{bytes_val:.2f} TB"

def run_speedtest():
    """Run the speed test and display results"""
    print("=" * 50)
    print("Internet Speed Test")
    print("=" * 50)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Initialize speedtest
        print("Initializing speedtest...")
        st = speedtest.Speedtest()
        
        # Get server list and select best server
        print("Finding best server...")
        st.get_best_server()
        
        # Display server information
        server = st.results.server
        print(f"Testing with server: {server['sponsor']} ({server['name']}, {server['country']})")
        print(f"Server distance: {server['d']:.2f} km")
        print()
        
        # Test download speed
        print("Testing download speed...")
        download_speed = st.download()
        print(f"Download speed: {format_speed(download_speed)}")
        
        # Test upload speed
        print("Testing upload speed...")
        upload_speed = st.upload()
        print(f"Upload speed: {format_speed(upload_speed)}")
        
        # Test ping
        print("Testing ping...")
        ping = st.results.ping
        print(f"Ping: {ping:.2f} ms")
        
        # Display results summary
        print()
        print("=" * 50)
        print("SPEED TEST RESULTS")
        print("=" * 50)
        print(f"Download: {format_speed(download_speed)}")
        print(f"Upload:   {format_speed(upload_speed)}")
        print(f"Ping:     {ping:.2f} ms")
        print(f"Server:   {server['sponsor']} ({server['name']})")
        print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Generate share URL (optional)
        try:
            print("\nGenerating results URL...")
            share_url = st.results.share()
            print(f"Results URL: {share_url}")
        except Exception as e:
            print(f"Could not generate share URL: {e}")
        
        return True
        
    except speedtest.ConfigRetrievalError:
        print("ERROR: Could not retrieve speedtest configuration.")
        print("Please check your internet connection.")
        return False
    except speedtest.NoMatchedServers:
        print("ERROR: No speedtest servers found.")
        return False
    except speedtest.SpeedtestException as e:
        print(f"ERROR: Speedtest failed - {e}")
        return False
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
        return False
    except Exception as e:
        print(f"ERROR: Unexpected error occurred - {e}")
        return False

def main():
    """Main function"""
    try:
        success = run_speedtest()
        if success:
            print("\nSpeed test completed successfully!")
        else:
            print("\nSpeed test failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\nExiting...")
        sys.exit(0)

if __name__ == "__main__":
    main()
