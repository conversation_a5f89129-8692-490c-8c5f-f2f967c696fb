def first_three_basic(input_string):
    result = ""
    count = 0
    if input_string == "":
        raise ValueError("Input string is empty")
    
    # Loop through each character
    for char in input_string:
        # Check if character is uppercase (ASCII values 65-90)
        if ord(char) >= 65 and ord(char) <= 90:
            # Convert to lowercase by adding 32 to ASCII value
            result += chr(ord(char) + 32)
        else:
            # Keep the character as is
            result += char
            
        count += 1
        # Stop after 3 characters
        if count >= 3:
            break
            
    return result

# Test cases
print(first_three_basic("HELLO"))     # Output: hel
print(first_three_basic("Python"))    # Output: pyt
print(first_three_basic("Hi"))        # Output: hi
print(first_three_basic(""))          # Output: empty string